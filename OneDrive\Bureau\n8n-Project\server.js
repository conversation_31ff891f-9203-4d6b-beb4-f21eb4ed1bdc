const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 3001;

app.use(cors());

// SSE route
app.get('/mcp/my-functions/sse', (req, res) => {
  res.set({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    Connection: 'keep-alive',
  });

  console.log('Client connecté au flux SSE');

  // Envoie un ping toutes les 5 secondes
  const interval = setInterval(() => {
    res.write(`data: ${JSON.stringify({ message: 'Ping MCP ok', timestamp: new Date() })}\n\n`);
  }, 5000);

  req.on('close', () => {
    console.log('Client déconnecté');
    clearInterval(interval);
  });
});

app.listen(PORT, () => {
  console.log(`✅ MCP Server en écoute sur http://localhost:${PORT}`);
});
